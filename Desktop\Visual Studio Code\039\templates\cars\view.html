{% extends "base.html" %}

{% block title %}تفاصيل السيارة - {{ car.make }} {{ car.model }} - معرض السيارات المتميز{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>تفاصيل السيارة</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('cars') }}">السيارات</a></li>
                    <li class="breadcrumb-item active">{{ car.make }} {{ car.model }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('cars') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
            <a href="{{ url_for('edit_car', car_id=car.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Car Images -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-camera me-2"></i>
                    صور السيارة
                </h5>
            </div>
            <div class="card-body">
                {% if car.photos %}
                    {% set photos = car.photos|from_json %}
                    {% if photos %}
                        <div id="carCarousel" class="carousel slide" data-bs-ride="carousel">
                            <div class="carousel-inner">
                                {% for photo in photos %}
                                <div class="carousel-item {{ 'active' if loop.first }}">
                                    <img src="{{ url_for('static', filename='uploads/cars/photos/' + photo) }}" 
                                         class="d-block w-100" style="height: 300px; object-fit: cover;">
                                </div>
                                {% endfor %}
                            </div>
                            {% if photos|length > 1 %}
                            <button class="carousel-control-prev" type="button" data-bs-target="#carCarousel" data-bs-slide="prev">
                                <span class="carousel-control-prev-icon"></span>
                            </button>
                            <button class="carousel-control-next" type="button" data-bs-target="#carCarousel" data-bs-slide="next">
                                <span class="carousel-control-next-icon"></span>
                            </button>
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-car fa-4x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد صور للسيارة</p>
                        </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-car fa-4x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد صور للسيارة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Car Details -->
    <div class="col-lg-6 mb-4">
        <!-- Basic Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>الماركة:</strong><br>
                        <span class="text-primary fs-5">{{ car.make }}</span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>الموديل:</strong><br>
                        <span class="text-primary fs-5">{{ car.model }}</span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>سنة الصنع:</strong><br>
                        <span class="badge bg-info fs-6">{{ car.year }}</span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>نوع السيارة:</strong><br>
                        {% if car.car_type == 'new' %}
                            <span class="badge bg-success fs-6">جديدة</span>
                        {% elif car.car_type == 'used' %}
                            <span class="badge bg-info fs-6">مستعملة</span>
                        {% else %}
                            <span class="badge bg-secondary fs-6">غير محدد</span>
                        {% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>رقم الشاسيه:</strong><br>
                        <code>{{ car.vin }}</code>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>اللون:</strong><br>
                        {{ car.color or 'غير محدد' }}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-flag me-2"></i>
                    الحالة والوضع
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>حالة السيارة:</strong><br>
                        {% if car.condition == 'excellent' %}
                            <span class="badge bg-success fs-6">ممتازة</span>
                        {% elif car.condition == 'good' %}
                            <span class="badge bg-info fs-6">جيدة</span>
                        {% elif car.condition == 'fair' %}
                            <span class="badge bg-warning fs-6">متوسطة</span>
                        {% elif car.condition == 'poor' %}
                            <span class="badge bg-danger fs-6">ضعيفة</span>
                        {% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>وضع السيارة:</strong><br>
                        {% if car.status == 'available' %}
                            <span class="badge bg-success fs-6">متاحة</span>
                        {% elif car.status == 'sold' %}
                            <span class="badge bg-primary fs-6">مباعة</span>
                        {% elif car.status == 'maintenance' %}
                            <span class="badge bg-warning fs-6">في الصيانة</span>
                        {% else %}
                            <span class="badge bg-secondary fs-6">{{ car.status }}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Technical Specs -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    المواصفات الفنية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>حجم المحرك:</strong><br>
                        {{ car.engine_size or 'غير محدد' }}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>نوع الوقود:</strong><br>
                        {% if car.fuel_type == 'gasoline' %}بنزين
                        {% elif car.fuel_type == 'diesel' %}ديزل
                        {% elif car.fuel_type == 'hybrid' %}هجين
                        {% elif car.fuel_type == 'electric' %}كهربائي
                        {% else %}غير محدد{% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>ناقل الحركة:</strong><br>
                        {% if car.transmission == 'manual' %}يدوي
                        {% elif car.transmission == 'automatic' %}أوتوماتيك
                        {% elif car.transmission == 'cvt' %}CVT
                        {% else %}غير محدد{% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>المسافة المقطوعة:</strong><br>
                        {% if car.mileage %}
                            {{ "{:,}".format(car.mileage) }} كم
                        {% else %}
                            غير محدد
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Pricing -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    الأسعار والربحية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if car.purchase_price %}
                    <div class="col-md-6 mb-3">
                        <strong>سعر الشراء:</strong><br>
                        <span class="text-danger fs-5">{{ "{:,.0f}".format(car.purchase_price) }} ريال</span>
                    </div>
                    {% endif %}
                    {% if car.selling_price %}
                    <div class="col-md-6 mb-3">
                        <strong>سعر البيع:</strong><br>
                        <span class="text-success fs-5">{{ "{:,.0f}".format(car.selling_price) }} ريال</span>
                    </div>
                    {% endif %}
                    {% if car.purchase_price and car.selling_price %}
                    <div class="col-12">
                        {% set profit = car.selling_price - car.purchase_price %}
                        <div class="alert {{ 'alert-success' if profit > 0 else 'alert-danger' if profit < 0 else 'alert-warning' }}">
                            <strong>الربح المتوقع:</strong>
                            <span class="fs-5">{{ "{:,.0f}".format(profit) }} ريال</span>
                            {% if profit > 0 %}
                                <i class="fas fa-arrow-up ms-2"></i>
                            {% elif profit < 0 %}
                                <i class="fas fa-arrow-down ms-2"></i>
                            {% else %}
                                <i class="fas fa-minus ms-2"></i>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Description -->
{% if car.description %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-align-left me-2"></i>
                    وصف السيارة
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ car.description }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Documents -->
{% if car.documents %}
    {% set documents = car.documents|from_json %}
    {% if documents %}
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        المستندات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for doc in documents %}
                        <div class="col-md-4 mb-3">
                            <div class="card border">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-pdf fa-2x text-danger mb-2"></i>
                                    <p class="small mb-2">{{ doc }}</p>
                                    <a href="{{ url_for('static', filename='uploads/cars/documents/' + doc) }}" 
                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-download me-1"></i>
                                        تحميل
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
{% endif %}

<!-- Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    الإجراءات المتاحة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('edit_car', car_id=car.id) }}" class="btn btn-warning w-100">
                            <i class="fas fa-edit me-2"></i>
                            تعديل البيانات
                        </a>
                    </div>
                    {% if car.status == 'available' %}
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_sale') }}?car_id={{ car.id }}" class="btn btn-success w-100">
                            <i class="fas fa-handshake me-2"></i>
                            تسجيل بيع
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-info w-100">
                            <i class="fas fa-tools me-2"></i>
                            إضافة صيانة
                        </a>
                    </div>
                    {% if current_user.role == 'owner' %}
                    <div class="col-md-3 mb-2">
                        <button type="button" class="btn btn-danger w-100" 
                                onclick="deleteCarConfirm({{ car.id }}, '{{ car.make }} {{ car.model }}')">
                            <i class="fas fa-trash me-2"></i>
                            حذف السيارة
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Car History -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    تاريخ السيارة
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">إضافة السيارة</h6>
                            <p class="timeline-text">تم إضافة السيارة إلى النظام</p>
                            <small class="text-muted">{{ car.created_at.strftime('%Y/%m/%d %H:%M') }}</small>
                        </div>
                    </div>
                    {% if car.updated_at and car.updated_at != car.created_at %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">آخر تحديث</h6>
                            <p class="timeline-text">تم تحديث بيانات السيارة</p>
                            <small class="text-muted">{{ car.updated_at.strftime('%Y/%m/%d %H:%M') }}</small>
                        </div>
                    </div>
                    {% endif %}
                    {% if car.purchase %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">عملية شراء</h6>
                            <p class="timeline-text">تم شراء السيارة من {{ car.purchase.customer.name }}</p>
                            <small class="text-muted">{{ car.purchase.purchase_date.strftime('%Y/%m/%d') }}</small>
                        </div>
                    </div>
                    {% endif %}
                    {% if car.sale %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">عملية بيع</h6>
                            <p class="timeline-text">تم بيع السيارة إلى {{ car.sale.customer.name }}</p>
                            <small class="text-muted">{{ car.sale.sale_date.strftime('%Y/%m/%d') }}</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #007bff;
    }
    
    .timeline-title {
        margin-bottom: 5px;
        color: #495057;
    }
    
    .timeline-text {
        margin-bottom: 5px;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Delete car confirmation
    function deleteCarConfirm(carId, carName) {
        if (confirm(`هل أنت متأكد من حذف السيارة: ${carName}؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!`)) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/cars/${carId}/delete`;
            
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}
