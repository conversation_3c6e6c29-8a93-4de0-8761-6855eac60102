from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import Login<PERSON>ana<PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash
import os
import json
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
import uuid

from config import config
from models import db, User, Customer, Car, Purchase, Sale, Inspection, MaintenanceRecord
from forms import (LoginForm, UserForm, CustomerForm, CarForm, PurchaseForm, 
                  SaleForm, InspectionForm, MaintenanceForm, SearchForm)

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Create upload directories
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['REPORTS_FOLDER'], exist_ok=True)
    os.makedirs(app.config['DOCUMENTS_FOLDER'], exist_ok=True)
    
    # Helper functions
    def allowed_file(filename):
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']
    
    def save_uploaded_file(file, folder='uploads'):
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            # Add timestamp to avoid conflicts
            name, ext = os.path.splitext(filename)
            filename = f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{ext}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], folder, filename)
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            file.save(filepath)
            return filename
        return None
    
    # Routes
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        
        form = LoginForm()
        if form.validate_on_submit():
            user = User.query.filter_by(username=form.username.data).first()
            if user and user.check_password(form.password.data) and user.is_active:
                login_user(user, remember=form.remember_me.data)
                user.last_login = datetime.utcnow()
                db.session.commit()
                
                next_page = request.args.get('next')
                if not next_page or not next_page.startswith('/'):
                    next_page = url_for('dashboard')
                return redirect(next_page)
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        
        return render_template('auth/login.html', form=form)
    
    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('login'))
    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        # Get statistics
        total_cars = Car.query.count()
        available_cars = Car.query.filter_by(status='available').count()
        sold_cars = Car.query.filter_by(status='sold').count()
        
        # Monthly earnings (current month)
        current_month = datetime.now().replace(day=1)
        monthly_sales = Sale.query.filter(Sale.sale_date >= current_month).all()
        monthly_earnings = sum(sale.sale_price for sale in monthly_sales)
        
        # Recent activities
        recent_sales = Sale.query.order_by(Sale.sale_date.desc()).limit(5).all()
        recent_purchases = Purchase.query.order_by(Purchase.purchase_date.desc()).limit(5).all()
        
        # Maintenance alerts (cars needing maintenance in next 30 days)
        next_month = datetime.now() + timedelta(days=30)
        maintenance_alerts = MaintenanceRecord.query.filter(
            MaintenanceRecord.next_maintenance_date <= next_month,
            MaintenanceRecord.next_maintenance_date >= datetime.now()
        ).all()
        
        return render_template('dashboard.html',
                             total_cars=total_cars,
                             available_cars=available_cars,
                             sold_cars=sold_cars,
                             monthly_earnings=monthly_earnings,
                             recent_sales=recent_sales,
                             recent_purchases=recent_purchases,
                             maintenance_alerts=maintenance_alerts)
    
    @app.route('/cars')
    @login_required
    def cars():
        page = request.args.get('page', 1, type=int)
        search = request.args.get('search', '')
        status_filter = request.args.get('status', '')
        car_type_filter = request.args.get('car_type', '')

        query = Car.query

        if search:
            query = query.filter(
                db.or_(
                    Car.make.contains(search),
                    Car.model.contains(search),
                    Car.vin.contains(search)
                )
            )

        if status_filter:
            query = query.filter_by(status=status_filter)

        if car_type_filter:
            query = query.filter_by(car_type=car_type_filter)

        cars = query.paginate(
            page=page,
            per_page=app.config['CARS_PER_PAGE'],
            error_out=False
        )

        return render_template('cars/list.html', cars=cars, search=search,
                             status_filter=status_filter, car_type_filter=car_type_filter)
    
    @app.route('/cars/add', methods=['GET', 'POST'])
    @login_required
    def add_car():
        form = CarForm()
        
        if form.validate_on_submit():
            car = Car(
                vin=form.vin.data,
                make=form.make.data,
                model=form.model.data,
                year=form.year.data,
                color=form.color.data,
                mileage=form.mileage.data,
                engine_size=form.engine_size.data,
                fuel_type=form.fuel_type.data,
                transmission=form.transmission.data,
                car_type=form.car_type.data,
                condition=form.condition.data,
                purchase_price=form.purchase_price.data,
                selling_price=form.selling_price.data,
                description=form.description.data
            )
            
            # Handle file uploads
            photos = []
            documents = []
            
            if form.photos.data:
                photo_filename = save_uploaded_file(form.photos.data, 'cars/photos')
                if photo_filename:
                    photos.append(photo_filename)
            
            if form.documents.data:
                doc_filename = save_uploaded_file(form.documents.data, 'cars/documents')
                if doc_filename:
                    documents.append(doc_filename)
            
            car.photos = json.dumps(photos)
            car.documents = json.dumps(documents)
            
            db.session.add(car)
            db.session.commit()
            
            flash('تم إضافة السيارة بنجاح', 'success')
            return redirect(url_for('cars'))
        
        return render_template('cars/add.html', form=form)
    
    @app.route('/customers')
    @login_required
    def customers():
        page = request.args.get('page', 1, type=int)
        search = request.args.get('search', '')
        
        query = Customer.query
        
        if search:
            query = query.filter(
                db.or_(
                    Customer.name.contains(search),
                    Customer.phone.contains(search),
                    Customer.national_id.contains(search)
                )
            )
        
        customers = query.paginate(
            page=page, 
            per_page=app.config['CUSTOMERS_PER_PAGE'], 
            error_out=False
        )
        
        return render_template('customers/list.html', customers=customers, search=search)
    
    @app.route('/customers/add', methods=['GET', 'POST'])
    @login_required
    def add_customer():
        form = CustomerForm()
        
        if form.validate_on_submit():
            customer = Customer(
                name=form.name.data,
                phone=form.phone.data,
                email=form.email.data,
                national_id=form.national_id.data,
                address=form.address.data,
                notes=form.notes.data
            )
            
            db.session.add(customer)
            db.session.commit()
            
            flash('تم إضافة العميل بنجاح', 'success')
            return redirect(url_for('customers'))
        
        return render_template('customers/add.html', form=form)

    @app.route('/cars/<int:car_id>')
    @login_required
    def view_car(car_id):
        car = Car.query.get_or_404(car_id)
        return render_template('cars/view.html', car=car)

    @app.route('/cars/<int:car_id>/edit', methods=['GET', 'POST'])
    @login_required
    def edit_car(car_id):
        car = Car.query.get_or_404(car_id)
        form = CarForm(obj=car)

        if form.validate_on_submit():
            car.vin = form.vin.data
            car.make = form.make.data
            car.model = form.model.data
            car.year = form.year.data
            car.color = form.color.data
            car.mileage = form.mileage.data
            car.engine_size = form.engine_size.data
            car.fuel_type = form.fuel_type.data
            car.transmission = form.transmission.data
            car.car_type = form.car_type.data
            car.condition = form.condition.data
            car.purchase_price = form.purchase_price.data
            car.selling_price = form.selling_price.data
            car.description = form.description.data
            car.updated_at = datetime.utcnow()

            # Handle file uploads
            if form.photos.data:
                photo_filename = save_uploaded_file(form.photos.data, 'cars/photos')
                if photo_filename:
                    existing_photos = json.loads(car.photos) if car.photos else []
                    existing_photos.append(photo_filename)
                    car.photos = json.dumps(existing_photos)

            if form.documents.data:
                doc_filename = save_uploaded_file(form.documents.data, 'cars/documents')
                if doc_filename:
                    existing_docs = json.loads(car.documents) if car.documents else []
                    existing_docs.append(doc_filename)
                    car.documents = json.dumps(existing_docs)

            db.session.commit()
            flash('تم تحديث بيانات السيارة بنجاح', 'success')
            return redirect(url_for('view_car', car_id=car.id))

        return render_template('cars/edit.html', form=form, car=car)

    @app.route('/cars/<int:car_id>/delete', methods=['POST'])
    @login_required
    def delete_car(car_id):
        if current_user.role not in ['owner']:
            flash('ليس لديك صلاحية لحذف السيارات', 'error')
            return redirect(url_for('cars'))

        car = Car.query.get_or_404(car_id)

        # Check if car has sales or purchases
        if car.sale or car.purchase:
            flash('لا يمكن حذف السيارة لأنها مرتبطة بمعاملات', 'error')
            return redirect(url_for('view_car', car_id=car.id))

        db.session.delete(car)
        db.session.commit()
        flash('تم حذف السيارة بنجاح', 'success')
        return redirect(url_for('cars'))

    @app.route('/sales')
    @login_required
    def sales():
        if current_user.role not in ['owner', 'sales']:
            flash('ليس لديك صلاحية للوصول إلى المبيعات', 'error')
            return redirect(url_for('dashboard'))

        page = request.args.get('page', 1, type=int)
        search = request.args.get('search', '')

        query = Sale.query.join(Car).join(Customer)

        if search:
            query = query.filter(
                db.or_(
                    Customer.name.contains(search),
                    Car.make.contains(search),
                    Car.model.contains(search),
                    Sale.contract_number.contains(search)
                )
            )

        sales = query.order_by(Sale.sale_date.desc()).paginate(
            page=page,
            per_page=app.config.get('TRANSACTIONS_PER_PAGE', 20),
            error_out=False
        )

        return render_template('sales/list.html', sales=sales, search=search)

    @app.route('/sales/add', methods=['GET', 'POST'])
    @login_required
    def add_sale():
        if current_user.role not in ['owner', 'sales']:
            flash('ليس لديك صلاحية لإضافة مبيعات', 'error')
            return redirect(url_for('dashboard'))

        form = SaleForm()

        # Populate choices for customers and available cars
        form.customer_id.choices = [(c.id, c.name) for c in Customer.query.all()]
        form.car_id.choices = [(c.id, f"{c.make} {c.model} {c.year}")
                              for c in Car.query.filter_by(status='available').all()]

        # Pre-select car if provided in URL
        car_id = request.args.get('car_id', type=int)
        if car_id and not form.car_id.data:
            car = Car.query.get(car_id)
            if car and car.status == 'available':
                form.car_id.data = car_id
                if car.selling_price:
                    form.sale_price.data = car.selling_price

        if form.validate_on_submit():
            # Check if car is still available
            car = Car.query.get(form.car_id.data)
            if car.status != 'available':
                flash('السيارة غير متاحة للبيع', 'error')
                return render_template('sales/add.html', form=form)

            # Generate contract number if not provided
            contract_number = form.contract_number.data
            if not contract_number:
                contract_number = f"SALE-{datetime.now().strftime('%Y%m%d')}-{Sale.query.count() + 1:04d}"

            sale = Sale(
                car_id=form.car_id.data,
                customer_id=form.customer_id.data,
                sale_price=form.sale_price.data,
                payment_method=form.payment_method.data,
                down_payment=form.down_payment.data,
                monthly_payment=form.monthly_payment.data,
                installment_months=form.installment_months.data,
                contract_number=contract_number,
                notes=form.notes.data,
                created_by=current_user.id
            )

            # Update car status
            car.status = 'sold'

            db.session.add(sale)
            db.session.commit()

            flash('تم تسجيل البيع بنجاح', 'success')
            return redirect(url_for('view_sale', sale_id=sale.id))

        return render_template('sales/add.html', form=form)

    @app.route('/sales/<int:sale_id>')
    @login_required
    def view_sale(sale_id):
        if current_user.role not in ['owner', 'sales']:
            flash('ليس لديك صلاحية لعرض المبيعات', 'error')
            return redirect(url_for('dashboard'))

        sale = Sale.query.get_or_404(sale_id)
        return render_template('sales/view.html', sale=sale)

    @app.route('/sales/<int:sale_id>/contract')
    @login_required
    def generate_contract(sale_id):
        if current_user.role not in ['owner', 'sales']:
            flash('ليس لديك صلاحية لإنشاء العقود', 'error')
            return redirect(url_for('dashboard'))

        sale = Sale.query.get_or_404(sale_id)

        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            from reportlab.lib.units import inch
            from reportlab.pdfbase import pdfutils
            from reportlab.pdfbase.ttfonts import TTFont
            from reportlab.pdfbase import pdfmetrics
            import io

            # Create PDF in memory
            buffer = io.BytesIO()
            p = canvas.Canvas(buffer, pagesize=A4)
            width, height = A4

            # Title
            p.setFont("Helvetica-Bold", 20)
            p.drawCentredText(width/2, height - 50, "Sales Contract / عقد البيع")

            # Contract details
            y_position = height - 100
            p.setFont("Helvetica", 12)

            contract_details = [
                f"Contract Number / رقم العقد: {sale.contract_number}",
                f"Date / التاريخ: {sale.sale_date.strftime('%Y/%m/%d')}",
                "",
                "Car Details / تفاصيل السيارة:",
                f"Make & Model / الماركة والموديل: {sale.car.make} {sale.car.model}",
                f"Year / السنة: {sale.car.year}",
                f"VIN / رقم الشاسيه: {sale.car.vin}",
                f"Color / اللون: {sale.car.color or 'N/A'}",
                "",
                "Customer Details / تفاصيل العميل:",
                f"Name / الاسم: {sale.customer.name}",
                f"Phone / الهاتف: {sale.customer.phone}",
                f"National ID / رقم الهوية: {sale.customer.national_id or 'N/A'}",
                "",
                "Payment Details / تفاصيل الدفع:",
                f"Sale Price / سعر البيع: {sale.sale_price:,.0f} SAR",
                f"Payment Method / طريقة الدفع: {sale.payment_method}",
            ]

            if sale.down_payment:
                contract_details.append(f"Down Payment / الدفعة المقدمة: {sale.down_payment:,.0f} SAR")
            if sale.monthly_payment:
                contract_details.append(f"Monthly Payment / القسط الشهري: {sale.monthly_payment:,.0f} SAR")
            if sale.installment_months:
                contract_details.append(f"Installment Period / فترة التقسيط: {sale.installment_months} months")

            for line in contract_details:
                p.drawString(50, y_position, line)
                y_position -= 20

            # Signature section
            y_position -= 40
            p.drawString(50, y_position, "Seller Signature / توقيع البائع: _________________")
            p.drawString(350, y_position, "Buyer Signature / توقيع المشتري: _________________")

            y_position -= 40
            p.drawString(50, y_position, f"Generated by: {current_user.full_name}")
            p.drawString(350, y_position, f"Date: {datetime.now().strftime('%Y/%m/%d %H:%M')}")

            p.showPage()
            p.save()

            buffer.seek(0)

            return send_file(
                buffer,
                as_attachment=True,
                download_name=f"contract_{sale.contract_number}.pdf",
                mimetype='application/pdf'
            )

        except ImportError:
            flash('مكتبة PDF غير متاحة. يرجى تثبيت reportlab', 'error')
            return redirect(url_for('view_sale', sale_id=sale_id))
        except Exception as e:
            flash(f'خطأ في إنشاء العقد: {str(e)}', 'error')
            return redirect(url_for('view_sale', sale_id=sale_id))

    @app.route('/sales/<int:sale_id>/invoice')
    @login_required
    def generate_invoice(sale_id):
        if current_user.role not in ['owner', 'sales']:
            flash('ليس لديك صلاحية لإنشاء الفواتير', 'error')
            return redirect(url_for('dashboard'))

        sale = Sale.query.get_or_404(sale_id)
        return render_template('sales/invoice.html', sale=sale)

    # Template filters
    @app.template_filter('from_json')
    def from_json_filter(value):
        if value:
            try:
                return json.loads(value)
            except:
                return []
        return []

    # Template globals
    @app.template_global()
    def moment():
        return datetime

    # Initialize database
    with app.app_context():
        db.create_all()

        # Create default admin user if not exists
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='owner'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=4000)
