# 🚗 نظام إدارة معرض السيارات المتميز
# Car Dealership Management System

نظام شامل لإدارة معارض السيارات باللغة العربية مع دعم كامل للاتجاه من اليمين إلى اليسار (RTL).

A comprehensive car dealership management system in Arabic with full RTL support.

## ✨ الميزات الرئيسية / Key Features

### 🔐 نظام المصادقة / Authentication System
- تسجيل دخول آمن بأسماء المستخدمين وكلمات المرور
- ثلاثة مستويات صلاحيات: مالك، موظف مبيعات، فني فحص
- خاصية "تذكرني" وإعادة تعيين كلمة المرور

### 📊 لوحة التحكم / Dashboard
- إحصائيات شاملة عن السيارات والمبيعات
- عرض الأرباح الشهرية
- تنبيهات الصيانة والترخيص
- آخر العمليات والأنشطة

### 🚙 إدارة السيارات / Car Management
- إضافة وتعديل وحذف السيارات
- رفع صور ومستندات السيارات
- تصنيف حسب النوع والسعر والحالة
- تتبع حالة السيارة (متاحة، مباعة، في الصيانة)

### 💰 إدارة المبيعات / Sales Management
- تسجيل عمليات البيع
- إصدار عقود البيع بصيغة PDF
- طرق دفع متعددة (نقدي، تقسيط، تحويل بنكي)
- فواتير رسمية

### 🛒 إدارة المشتريات / Purchase Management
- تسجيل عمليات الشراء من البائعين
- أرشفة المستندات
- تسجيل حالة السيارة عند الشراء

### 👥 إدارة العملاء / Customer Management
- إضافة وإدارة بيانات العملاء
- سجل المعاملات لكل عميل
- إشعارات العملاء

### 🔧 قسم الصيانة والفحص / Maintenance & Inspection
- تسجيل الفحص الفني
- تكاليف الصيانة
- تذكير الصيانة التلقائي

### 🔍 البحث المتقدم / Advanced Search
- البحث برقم الشاسيه، النوع، الحالة، السعر
- نتائج فورية ومرشحة

### 📈 التقارير والطباعة / Reports & Printing
- تقارير المبيعات اليومية والشهرية
- تقارير الأرباح
- تصدير PDF وExcel

## 🛠️ التقنيات المستخدمة / Technologies Used

- **Backend**: Python Flask
- **Database**: SQLite (قابل للترقية إلى MySQL)
- **Frontend**: HTML5, CSS3, Bootstrap 5 RTL
- **JavaScript**: jQuery, Chart.js
- **PDF Generation**: ReportLab
- **Excel Export**: OpenPyXL
- **Authentication**: Flask-Login
- **Forms**: Flask-WTF
- **Database ORM**: SQLAlchemy

## 📋 متطلبات النظام / System Requirements

- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, Linux
- ذاكرة: 512 MB RAM كحد أدنى
- مساحة القرص: 100 MB

## 🚀 التثبيت والتشغيل / Installation & Setup

### الطريقة السريعة / Quick Setup

```bash
# 1. تثبيت المتطلبات / Install requirements
python setup.py

# 2. تشغيل النظام / Run the system
python run.py
```

### التثبيت اليدوي / Manual Installation

```bash
# 1. تثبيت المكتبات المطلوبة / Install dependencies
pip install -r requirements.txt

# 2. إنشاء المجلدات / Create directories
mkdir -p static/uploads/cars/{photos,documents}
mkdir -p static/documents reports

# 3. تشغيل النظام / Run the application
python app.py
```

## 🔑 بيانات الدخول الافتراضية / Default Login Credentials

- **اسم المستخدم / Username**: `admin`
- **كلمة المرور / Password**: `admin123`
- **الصلاحية / Role**: مالك (Owner)

## 📱 الوصول للنظام / System Access

بعد تشغيل النظام، افتح المتصفح على:
After running the system, open your browser at:

```
http://localhost:4000
```

## 📁 هيكل المشروع / Project Structure

```
car-dealership/
├── app.py                 # التطبيق الرئيسي / Main application
├── models.py              # نماذج قاعدة البيانات / Database models
├── forms.py               # نماذج الإدخال / Input forms
├── config.py              # إعدادات النظام / System configuration
├── requirements.txt       # المتطلبات / Dependencies
├── run.py                 # ملف التشغيل / Run script
├── setup.py               # ملف الإعداد / Setup script
├── templates/             # قوالب HTML / HTML templates
│   ├── base.html
│   ├── dashboard.html
│   ├── auth/
│   ├── cars/
│   ├── customers/
│   ├── sales/
│   └── reports/
├── static/                # الملفات الثابتة / Static files
│   ├── css/
│   ├── js/
│   └── uploads/
└── reports/               # التقارير المُنتجة / Generated reports
```

## 🎯 الاستخدام / Usage

### إضافة سيارة جديدة / Adding a New Car
1. انتقل إلى "إدارة السيارات" / Go to "Car Management"
2. اضغط "إضافة سيارة جديدة" / Click "Add New Car"
3. املأ البيانات المطلوبة / Fill required information
4. ارفع الصور والمستندات / Upload photos and documents
5. احفظ البيانات / Save the data

### تسجيل عملية بيع / Recording a Sale
1. انتقل إلى "المبيعات" / Go to "Sales"
2. اختر "بيع جديد" / Select "New Sale"
3. اختر العميل والسيارة / Choose customer and car
4. أدخل تفاصيل البيع / Enter sale details
5. احفظ العملية / Save the transaction

## 🔧 التخصيص / Customization

### تغيير معلومات الشركة / Company Information
عدّل الملف `config.py`:
Edit the `config.py` file:

```python
COMPANY_NAME = 'اسم معرضك'
COMPANY_ADDRESS = 'عنوان المعرض'
COMPANY_PHONE = 'رقم الهاتف'
```

### إضافة مستخدمين جدد / Adding New Users
1. سجل دخول كمالك / Login as owner
2. انتقل إلى "الإعدادات" / Go to "Settings"
3. اختر "إدارة المستخدمين" / Select "User Management"
4. أضف مستخدم جديد / Add new user

## 🛡️ الأمان / Security

- كلمات مرور مشفرة / Encrypted passwords
- جلسات آمنة / Secure sessions
- حماية من CSRF / CSRF protection
- تحقق من صحة الملفات المرفوعة / File upload validation

## 📞 الدعم الفني / Technical Support

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
For technical support or to report issues:

- إنشاء issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق

## 📄 الترخيص / License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.
This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 المساهمة / Contributing

نرحب بالمساهمات! يرجى:
Contributions are welcome! Please:

1. Fork المشروع / Fork the project
2. إنشاء branch للميزة الجديدة / Create a feature branch
3. Commit التغييرات / Commit your changes
4. Push إلى البranch / Push to the branch
5. إنشاء Pull Request / Create a Pull Request

## 📝 سجل التغييرات / Changelog

### الإصدار 1.0.0 / Version 1.0.0
- إطلاق النسخة الأولى / Initial release
- جميع الميزات الأساسية / All core features
- دعم اللغة العربية / Arabic language support
- واجهة RTL / RTL interface

---

**تم تطويره بـ ❤️ للمجتمع العربي**
**Developed with ❤️ for the Arabic community**
