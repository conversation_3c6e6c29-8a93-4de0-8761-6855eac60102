<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}معرض السيارات المتميز{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .stats-card .stats-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            color: #333;
            font-weight: 700;
            margin: 0;
        }
        
        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
        }
        
        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
        }
        
        .breadcrumb-item.active {
            color: #6c757d;
        }

        /* Floating Action Button */
        .floating-action-btn {
            position: fixed;
            bottom: 30px;
            left: 30px;
            z-index: 1000;
        }

        .fab-container {
            position: relative;
        }

        .fab-main {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 20px rgba(40, 167, 69, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fab-main:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(40, 167, 69, 0.6);
        }

        .fab-main.active {
            transform: rotate(45deg);
        }

        .fab-options {
            position: absolute;
            bottom: 70px;
            left: 0;
            display: flex;
            flex-direction: column;
            gap: 10px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .fab-options.show {
            opacity: 1;
            visibility: visible;
        }

        .fab-option {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            box-shadow: 0 3px 15px rgba(0, 123, 255, 0.4);
            transition: all 0.3s ease;
            transform: scale(0);
        }

        .fab-options.show .fab-option {
            transform: scale(1);
        }

        .fab-options.show .fab-option:nth-child(1) {
            transition-delay: 0.1s;
            background: #28a745;
        }

        .fab-options.show .fab-option:nth-child(2) {
            transition-delay: 0.2s;
            background: #17a2b8;
        }

        .fab-options.show .fab-option:nth-child(3) {
            transition-delay: 0.3s;
            background: #ffc107;
        }

        .fab-option:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        /* RTL adjustments for FAB */
        [dir="rtl"] .floating-action-btn {
            left: auto;
            right: 30px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if current_user.is_authenticated %}
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">معرض السيارات</h4>
                        <small class="text-white-50">مرحباً، {{ current_user.full_name }}</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'dashboard' }}" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'cars' in request.endpoint }}" href="{{ url_for('cars') }}">
                                <i class="fas fa-car me-2"></i>
                                إدارة السيارات
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'sales' in request.endpoint }}" href="{{ url_for('sales') }}">
                                <i class="fas fa-handshake me-2"></i>
                                المبيعات
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'customers' in request.endpoint }}" href="{{ url_for('customers') }}">
                                <i class="fas fa-users me-2"></i>
                                إدارة العملاء
                            </a>
                        </li>

                        {% if current_user.role in ['owner', 'sales'] %}
                        <li class="nav-item">
                            <a class="nav-link" href="#sales-submenu" data-bs-toggle="collapse">
                                <i class="fas fa-shopping-cart me-2"></i>
                                المبيعات المتقدمة
                                <i class="fas fa-chevron-down float-end"></i>
                            </a>
                            <div class="collapse" id="sales-submenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('add_sale') }}">
                                            <i class="fas fa-plus me-2"></i>
                                            بيع جديد
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('sales') }}">
                                            <i class="fas fa-list me-2"></i>
                                            قائمة المبيعات
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        {% endif %}

                        <li class="nav-item">
                            <a class="nav-link" href="#purchases-submenu" data-bs-toggle="collapse">
                                <i class="fas fa-shopping-bag me-2"></i>
                                المشتريات
                                <i class="fas fa-chevron-down float-end"></i>
                            </a>
                            <div class="collapse" id="purchases-submenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-plus me-2"></i>
                                            شراء جديد
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-list me-2"></i>
                                            قائمة المشتريات
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        {% endif %}
                        {% if current_user.role in ['owner', 'technician'] %}
                        <li class="nav-item">
                            <a class="nav-link" href="#maintenance-submenu" data-bs-toggle="collapse">
                                <i class="fas fa-tools me-2"></i>
                                الصيانة والفحص
                                <i class="fas fa-chevron-down float-end"></i>
                            </a>
                            <div class="collapse" id="maintenance-submenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-search me-2"></i>
                                            فحص فني
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-wrench me-2"></i>
                                            سجل الصيانة
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        {% endif %}
                        {% if current_user.role == 'owner' %}
                        <li class="nav-item">
                            <a class="nav-link" href="#reports-submenu" data-bs-toggle="collapse">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                                <i class="fas fa-chevron-down float-end"></i>
                            </a>
                            <div class="collapse" id="reports-submenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-file-pdf me-2"></i>
                                            تقرير المبيعات
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-file-excel me-2"></i>
                                            تقرير الأرباح
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                        {% endif %}
                        <li class="nav-item mt-3">
                            <a class="nav-link" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Floating Action Button for Quick Sale -->
    {% if current_user.is_authenticated and current_user.role in ['owner', 'sales'] %}
    <div class="floating-action-btn">
        <div class="fab-container">
            <button class="fab-main" id="fabMain">
                <i class="fas fa-plus"></i>
            </button>
            <div class="fab-options" id="fabOptions">
                <a href="{{ url_for('add_sale') }}" class="fab-option" title="تسجيل بيع جديد">
                    <i class="fas fa-handshake"></i>
                </a>
                <a href="{{ url_for('add_car') }}" class="fab-option" title="إضافة سيارة جديدة">
                    <i class="fas fa-car"></i>
                </a>
                <a href="{{ url_for('add_customer') }}" class="fab-option" title="إضافة عميل جديد">
                    <i class="fas fa-user-plus"></i>
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    {% else %}
        {% block auth_content %}{% endblock %}
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Floating Action Button Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fabMain = document.getElementById('fabMain');
            const fabOptions = document.getElementById('fabOptions');

            if (fabMain && fabOptions) {
                fabMain.addEventListener('click', function() {
                    fabMain.classList.toggle('active');
                    fabOptions.classList.toggle('show');
                });

                // Close FAB when clicking outside
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.fab-container')) {
                        fabMain.classList.remove('active');
                        fabOptions.classList.remove('show');
                    }
                });

                // Add tooltips to FAB options
                const fabOptionElements = document.querySelectorAll('.fab-option');
                fabOptionElements.forEach(function(option) {
                    option.addEventListener('mouseenter', function() {
                        const title = this.getAttribute('title');
                        if (title) {
                            // Create tooltip
                            const tooltip = document.createElement('div');
                            tooltip.className = 'fab-tooltip';
                            tooltip.textContent = title;
                            tooltip.style.cssText = `
                                position: absolute;
                                right: 60px;
                                top: 50%;
                                transform: translateY(-50%);
                                background: rgba(0, 0, 0, 0.8);
                                color: white;
                                padding: 5px 10px;
                                border-radius: 4px;
                                font-size: 12px;
                                white-space: nowrap;
                                z-index: 1001;
                                pointer-events: none;
                            `;
                            this.appendChild(tooltip);
                        }
                    });

                    option.addEventListener('mouseleave', function() {
                        const tooltip = this.querySelector('.fab-tooltip');
                        if (tooltip) {
                            tooltip.remove();
                        }
                    });
                });
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
