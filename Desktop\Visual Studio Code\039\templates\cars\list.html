{% extends "base.html" %}

{% block title %}إدارة السيارات - معرض السيارات المتميز{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>إدارة السيارات</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item active">السيارات</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('add_car') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة سيارة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="البحث بالماركة، الموديل، أو رقم الشاسيه" 
                       value="{{ search }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">حالة السيارة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="available" {{ 'selected' if status_filter == 'available' }}>متاحة</option>
                    <option value="sold" {{ 'selected' if status_filter == 'sold' }}>مباعة</option>
                    <option value="maintenance" {{ 'selected' if status_filter == 'maintenance' }}>في الصيانة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">نوع السيارة</label>
                <select name="car_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="new" {{ 'selected' if request.args.get('car_type') == 'new' }}>جديدة</option>
                    <option value="used" {{ 'selected' if request.args.get('car_type') == 'used' }}>مستعملة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('cars') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Cars List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-car me-2"></i>
            قائمة السيارات
            <span class="badge bg-primary ms-2">{{ cars.total }} سيارة</span>
        </h5>
    </div>
    <div class="card-body">
        {% if cars.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>المعلومات الأساسية</th>
                            <th>التفاصيل</th>
                            <th>الأسعار</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for car in cars.items %}
                        <tr>
                            <td>
                                {% if car.photos %}
                                    {% set photos = car.photos|from_json %}
                                    {% if photos %}
                                        <img src="{{ url_for('static', filename='uploads/cars/photos/' + photos[0]) }}" 
                                             class="img-thumbnail" style="width: 80px; height: 60px; object-fit: cover;">
                                    {% else %}
                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                             style="width: 80px; height: 60px;">
                                            <i class="fas fa-car text-muted"></i>
                                        </div>
                                    {% endif %}
                                {% else %}
                                    <div class="bg-light d-flex align-items-center justify-content-center" 
                                         style="width: 80px; height: 60px;">
                                        <i class="fas fa-car text-muted"></i>
                                    </div>
                                {% endif %}
                            </td>
                            <td>
                                <div class="fw-bold">{{ car.make }} {{ car.model }}</div>
                                <div class="text-muted small">{{ car.year }}</div>
                                <div class="text-muted small">{{ car.vin }}</div>
                            </td>
                            <td>
                                <div>
                                    <strong>النوع:</strong>
                                    {% if car.car_type == 'new' %}
                                        <span class="badge bg-success">جديدة</span>
                                    {% elif car.car_type == 'used' %}
                                        <span class="badge bg-info">مستعملة</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير محدد</span>
                                    {% endif %}
                                </div>
                                <div><strong>اللون:</strong> {{ car.color or 'غير محدد' }}</div>
                                <div><strong>المسافة:</strong> {{ "{:,}".format(car.mileage) if car.mileage else 'غير محدد' }} كم</div>
                                <div><strong>الوقود:</strong>
                                    {% if car.fuel_type == 'gasoline' %}بنزين
                                    {% elif car.fuel_type == 'diesel' %}ديزل
                                    {% elif car.fuel_type == 'hybrid' %}هجين
                                    {% elif car.fuel_type == 'electric' %}كهربائي
                                    {% else %}غير محدد{% endif %}
                                </div>
                            </td>
                            <td>
                                {% if car.purchase_price %}
                                    <div><strong>الشراء:</strong> {{ "{:,.0f}".format(car.purchase_price) }} ريال</div>
                                {% endif %}
                                {% if car.selling_price %}
                                    <div><strong>البيع:</strong> {{ "{:,.0f}".format(car.selling_price) }} ريال</div>
                                {% endif %}
                                {% if car.purchase_price and car.selling_price %}
                                    {% set profit = car.selling_price - car.purchase_price %}
                                    <div class="small {{ 'text-success' if profit > 0 else 'text-danger' }}">
                                        <strong>الربح:</strong> {{ "{:,.0f}".format(profit) }} ريال
                                    </div>
                                {% endif %}
                            </td>
                            <td>
                                {% if car.status == 'available' %}
                                    <span class="badge bg-success">متاحة</span>
                                {% elif car.status == 'sold' %}
                                    <span class="badge bg-primary">مباعة</span>
                                {% elif car.status == 'maintenance' %}
                                    <span class="badge bg-warning">في الصيانة</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ car.status }}</span>
                                {% endif %}
                                
                                <div class="mt-1">
                                    {% if car.condition == 'excellent' %}
                                        <small class="text-success">ممتازة</small>
                                    {% elif car.condition == 'good' %}
                                        <small class="text-info">جيدة</small>
                                    {% elif car.condition == 'fair' %}
                                        <small class="text-warning">متوسطة</small>
                                    {% elif car.condition == 'poor' %}
                                        <small class="text-danger">ضعيفة</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="btn-group-vertical btn-group-sm" role="group">
                                    <a href="{{ url_for('view_car', car_id=car.id) }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <a href="{{ url_for('edit_car', car_id=car.id) }}" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>

                                    <a href="#" class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-tools"></i> صيانة
                                    </a>
                                    {% if current_user.role == 'owner' %}
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="deleteCarConfirm({{ car.id }}, '{{ car.make }} {{ car.model }}')">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if cars.pages > 1 %}
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if cars.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('cars', page=cars.prev_num, search=search, status=status_filter) }}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in cars.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != cars.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('cars', page=page_num, search=search, status=status_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if cars.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('cars', page=cars.next_num, search=search, status=status_filter) }}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-car fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد سيارات</h4>
                <p class="text-muted">لم يتم العثور على أي سيارات تطابق معايير البحث</p>
                <a href="{{ url_for('add_car') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة سيارة جديدة
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Delete car confirmation
    function deleteCarConfirm(carId, carName) {
        if (confirm(`هل أنت متأكد من حذف السيارة: ${carName}؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!`)) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/cars/${carId}/delete`;

            // Add CSRF token
            const csrfToken = document.querySelector('meta[name=csrf-token]');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrf_token';
                csrfInput.value = csrfToken.getAttribute('content');
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    }

    // Auto-submit search form on filters change
    document.querySelector('select[name="status"]').addEventListener('change', function() {
        this.form.submit();
    });

    document.querySelector('select[name="car_type"]').addEventListener('change', function() {
        this.form.submit();
    });

    // Add loading state to search button
    document.querySelector('form').addEventListener('submit', function() {
        const searchBtn = document.querySelector('button[type="submit"]');
        const originalText = searchBtn.innerHTML;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري البحث...';
        searchBtn.disabled = true;

        // Re-enable after 3 seconds
        setTimeout(function() {
            searchBtn.innerHTML = originalText;
            searchBtn.disabled = false;
        }, 3000);
    });
</script>
{% endblock %}
