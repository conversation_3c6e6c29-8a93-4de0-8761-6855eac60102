#!/usr/bin/env python3
"""
Setup script for Car Dealership Management System
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("🔧 تثبيت المتطلبات...")
    print("Installing requirements...")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ تم تثبيت جميع المتطلبات بنجاح")
        print("✅ All requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        print(f"❌ Error installing requirements: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 إنشاء المجلدات المطلوبة...")
    print("Creating required directories...")
    
    directories = [
        'static/uploads/cars/photos',
        'static/uploads/cars/documents',
        'static/uploads/customers',
        'static/documents',
        'reports',
        'instance'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   ✓ {directory}")
    
    print("✅ تم إنشاء جميع المجلدات")
    print("✅ All directories created")

def setup_database():
    """Initialize the database"""
    print("🗄️ إعداد قاعدة البيانات...")
    print("Setting up database...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.app_context():
            from models import db
            db.create_all()
            print("✅ تم إعداد قاعدة البيانات بنجاح")
            print("✅ Database setup completed")
            return True
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        print(f"❌ Error setting up database: {e}")
        return False

def main():
    """Main setup function"""
    print("=" * 60)
    print("🚗 إعداد نظام إدارة معرض السيارات")
    print("Car Dealership Management System Setup")
    print("=" * 60)
    
    # Step 1: Install requirements
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        print("❌ Failed to install requirements")
        sys.exit(1)
    
    print()
    
    # Step 2: Create directories
    create_directories()
    
    print()
    
    # Step 3: Setup database
    if not setup_database():
        print("❌ فشل في إعداد قاعدة البيانات")
        print("❌ Failed to setup database")
        sys.exit(1)
    
    print()
    print("=" * 60)
    print("🎉 تم إعداد النظام بنجاح!")
    print("🎉 Setup completed successfully!")
    print("=" * 60)
    print("📋 الخطوات التالية:")
    print("Next steps:")
    print("   1. python run.py  # لتشغيل النظام")
    print("   1. python run.py  # To run the system")
    print("   2. افتح المتصفح على: http://localhost:4000")
    print("   2. Open browser at: http://localhost:4000")
    print("   3. اسم المستخدم: admin")
    print("   3. Username: admin")
    print("   4. كلمة المرور: admin123")
    print("   4. Password: admin123")
    print("=" * 60)

if __name__ == '__main__':
    main()
