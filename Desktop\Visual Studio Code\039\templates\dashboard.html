{% extends "base.html" %}

{% block title %}لوحة التحكم - معرض السيارات المتميز{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>لوحة التحكم</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item active">الرئيسية</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex align-items-center gap-2">
            <a href="{{ url_for('add_sale') }}" class="btn btn-success btn-sm">
                <i class="fas fa-handshake me-1"></i>
                بيع سريع
            </a>
            <span class="text-muted" id="current-time"></span>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ total_cars }}</div>
                    <div class="stats-label">إجمالي السيارات</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-car fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ available_cars }}</div>
                    <div class="stats-label">السيارات المتاحة</div>
                    <div class="mt-2">
                        <a href="{{ url_for('cars', status='available') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-handshake me-1"></i>
                            بيع سريع
                        </a>
                    </div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ sold_cars }}</div>
                    <div class="stats-label">السيارات المباعة</div>
                    <div class="mt-2">
                        <a href="{{ url_for('sales') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-chart-line me-1"></i>
                            عرض المبيعات
                        </a>
                    </div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-handshake fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ "{:,.0f}".format(monthly_earnings) }}</div>
                    <div class="stats-label">الأرباح الشهرية (ريال)</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Sales -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    آخر المبيعات
                </h5>
            </div>
            <div class="card-body">
                {% if recent_sales %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>السيارة</th>
                                    <th>السعر</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in recent_sales %}
                                <tr>
                                    <td>{{ sale.customer.name }}</td>
                                    <td>{{ sale.car.make }} {{ sale.car.model }}</td>
                                    <td>{{ "{:,.0f}".format(sale.sale_price) }} ريال</td>
                                    <td>{{ sale.sale_date.strftime('%Y/%m/%d') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('sales') }}" class="btn btn-outline-primary btn-sm">عرض جميع المبيعات</a>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مبيعات حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Purchases -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-bag me-2"></i>
                    آخر المشتريات
                </h5>
            </div>
            <div class="card-body">
                {% if recent_purchases %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>البائع</th>
                                    <th>السيارة</th>
                                    <th>السعر</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for purchase in recent_purchases %}
                                <tr>
                                    <td>{{ purchase.customer.name }}</td>
                                    <td>{{ purchase.car.make }} {{ purchase.car.model }}</td>
                                    <td>{{ "{:,.0f}".format(purchase.purchase_price) }} ريال</td>
                                    <td>{{ purchase.purchase_date.strftime('%Y/%m/%d') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="#" class="btn btn-outline-primary btn-sm">عرض جميع المشتريات</a>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مشتريات حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Maintenance Alerts -->
{% if maintenance_alerts %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تنبيهات الصيانة
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>السيارة</th>
                                <th>نوع الصيانة</th>
                                <th>تاريخ الصيانة القادمة</th>
                                <th>الأيام المتبقية</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for alert in maintenance_alerts %}
                            <tr>
                                <td>{{ alert.car.make }} {{ alert.car.model }} ({{ alert.car.year }})</td>
                                <td>{{ alert.maintenance_type }}</td>
                                <td>{{ alert.next_maintenance_date.strftime('%Y/%m/%d') }}</td>
                                <td>
                                    {% if alert.next_maintenance_date %}
                                        <span class="badge bg-warning">
                                            قريباً
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-outline-primary">جدولة صيانة</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('add_car') }}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                            إضافة سيارة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('add_customer') }}" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                            إضافة عميل جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('add_sale') }}" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-handshake fa-2x d-block mb-2"></i>
                            تسجيل بيع جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('sales') }}" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-chart-line fa-2x d-block mb-2"></i>
                            عرض المبيعات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
    
    // Add current time display
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        // Update time display if element exists
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }
    
    // Update time every minute
    setInterval(updateTime, 60000);
    updateTime();
</script>
{% endblock %}
