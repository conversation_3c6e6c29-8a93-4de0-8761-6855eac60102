import os
from datetime import timedelta

class Config:
    # Basic Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'car-dealership-secret-key-2024'
    
    # Database configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///car_dealership.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Upload configuration
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'doc', 'docx'}
    
    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # Language and localization
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }
    DEFAULT_LANGUAGE = 'ar'
    
    # Pagination
    CARS_PER_PAGE = 10
    CUSTOMERS_PER_PAGE = 15
    TRANSACTIONS_PER_PAGE = 20
    
    # File paths
    REPORTS_FOLDER = 'reports'
    DOCUMENTS_FOLDER = 'static/documents'
    
    # Company information
    COMPANY_NAME = 'معرض السيارات المتميز'
    COMPANY_NAME_EN = 'Premium Car Showroom'
    COMPANY_ADDRESS = 'الرياض، المملكة العربية السعودية'
    COMPANY_PHONE = '+966 11 123 4567'
    COMPANY_EMAIL = '<EMAIL>'
    
    # User roles
    USER_ROLES = {
        'owner': 'مالك',
        'sales': 'موظف مبيعات', 
        'technician': 'فني فحص'
    }

class DevelopmentConfig(Config):
    DEBUG = True
    
class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
